<!-- FAQs Start -->
<div class="container-fluid faq-section py-5">
    <div class="container py-5">
        <div class="row g-5 align-items-center">
            <div class="col-xl-6 wow fadeInLeft" data-wow-delay="0.2s">
                <div class="h-100">
                    <div class="mb-5">
                        {{-- <h4 class="text-primary">Some Important FAQ's</h4> --}}
                        <h1 class="display-4 mb-0">Consultor&iacute;as:</h1>
                    </div>
                    <div class="accordion" id="accordionExample">
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="headingOne">
                                <button class="accordion-button border-0" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne" aria-expanded="true" aria-controls="collapseOne">
                                    Planeación Estratégica por Cuadro de Mando Integral
                                </button>
                            </h2>
                            <div id="collapseOne" class="accordion-collapse collapse show active" aria-labelledby="headingOne" data-bs-parent="#accordionExample">
                                <div class="accordion-body rounded">
                                    <span class="badge" style="background-color: #17385A">LOGRO/RESULTADO:</span>
                                    Se coordinan sesiones de trabajo con personal clave de la organización para elaborar el plan estratégico y operativo de la organización, así como otros instrumentos requeridos para el funcionamiento en el corto plazo y su sostenibilidad en el mediano y largo plazo.<br>
                                    <br>
                                    <span class="badge" style="background-color: #17385A">DESCRIPCIÓN DEL PROCESO:</span>
                                    <ul>
                                        <li>Pensamiento filosófico: Misión, Visión y Valores</li>
                                        <li>Objetivos y Ejes Estratégicos: Las grandes líneas estratégicas</li>
                                        <li>Táctica: Iniciativas estratégicas y retos claves</li>
                                        <li>Indicadores (KPI); BSC, metas, mediciones, mointoreo y seguimiento</li>
                                        <li>Planes Operativos: Marco Lógico, actividades, resultados, responsables y fechas</li>
                                    </ul>
                                    <span class="badge" style="background-color: #17385A">PRODUCTOS A ENTREGAR:</span>
                                    <ul>
                                        <li>Plan Estratégico Institucional</li>
                                        <li>Planes Operativos</li>
                                        <li>
                                            <div class="gallery-item w-100">
                                                <div class="gallery-img">
                                                    Manual&nbsp;<i class="fas fa-book"></i>
                                                    <div class="gallery-overlay">
                                                        <a href="{{ asset('img/consultorias/ayd_plan_estrategico.jpg') }}" data-lightbox="gallery" class="gallery-btn">
                                                            <i class="fas fa-search-plus text-white"></i>
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>


                        <div class="accordion-item">
                            <h2 class="accordion-header" id="headingTwo">
                                <button class="accordion-button collapsed border-0" type="button" data-bs-toggle="collapse" data-bs-target="#collapseTwo" aria-expanded="false" aria-controls="collapseTwo">
                                    Diseño de Estructura Organizativa y Manual de Organización
                                </button>
                            </h2>
                            <div id="collapseTwo" class="accordion-collapse collapse" aria-labelledby="headingTwo" data-bs-parent="#accordionExample">
                                <div class="accordion-body rounded">
                                    <span class="badge" style="background-color: #17385A">LOGRO/RESULTADO:</span>
                                    Se realiza una evaluación que permite un diagnóstico previo de la situación actual de la organización para definir la nueva Estructura Organizativa que permita analizar su funcionamiento con respecto a los puestos y funciones definidos para facilitar su funcionamiento administrativo y operativo.<br><br>

                                    <span class="badge" style="background-color: #17385A">DESCRIPCIÓN DEL PROCESO:</span>
                                    <ul>
                                        <li>Diagnóstico de Estructura Vigente</li>
                                        <li>Diseño de Estructura Organizativa Propuesta</li>
                                        <li>Validación de Estructura</li>
                                        <li>Elaboración de Manual de Organización</li>
                                    </ul>

                                    <span class="badge" style="background-color: #17385A">PRODUCTOS A ENTREGAR:</span>
                                    <ul>
                                        <li>Estructura Organizativa Actualizada</li>
                                        <li>Manual de Organización</li>
                                        <li>
                                            <div class="gallery-item w-100">
                                                <div class="gallery-img">
                                                    Manual&nbsp;<i class="fas fa-book"></i>
                                                    <div class="gallery-overlay">
                                                        <a href="{{ asset('img/consultorias/ayd_manual_organizacional.jpg') }}" data-lightbox="gallery" class="gallery-btn">
                                                            <i class="fas fa-search-plus text-white"></i>
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div class="accordion-item">
                            <h2 class="accordion-header" id="headingThree">
                                <button class="accordion-button collapsed border-0" type="button" data-bs-toggle="collapse" data-bs-target="#collapseThree" aria-expanded="false" aria-controls="collapseThree">
                                    Manual de Funciones y Descriptores de Cargos
                                </button>
                            </h2>
                            <div id="collapseThree" class="accordion-collapse collapse" aria-labelledby="headingThree" data-bs-parent="#accordionExample">
                                <div class="accordion-body rounded">
                                    <span class="badge" style="background-color: #17385A">LOGRO/RESULTADO:</span>
                                    Con la metodología del Mapa Funcional, se realiza un diseño/actualización del Manual de descriptores de cargos y se define/actualiza el diccionario por competencias que sean la base para definir competencias específicas en el personal.<br><br>

                                    <span class="badge" style="background-color: #17385A">DESCRIPCIÓN DEL PROCESO:</span>
                                    <ul>
                                        <li>Talleres de Diseño de Mapa Funcional</li>
                                        <li>Elaboración de Mapa Funcional Global</li>
                                        <li>Diseño de Catálogos (Diccionario) de Competencias</li>
                                        <li>Diccionario de Competencias</li>
                                        <li>Niveles de Dominio de Competencias por Grupo Ocupacional</li>
                                    </ul>

                                    <span class="badge" style="background-color: #17385A">PRODUCTOS A ENTREGAR:</span>
                                    <ul>
                                        <li>Diccionario de Competencias</li>
                                        <li>Manual de Puestos y Funciones</li>
                                        <li>
                                            <div class="gallery-item w-100">
                                                <div class="gallery-img">
                                                    Manual&nbsp;<i class="fas fa-book"></i>
                                                    <div class="gallery-overlay">
                                                        <a href="{{ asset('img/consultorias/ayd_manual_cargos.jpg') }}" data-lightbox="gallery" class="gallery-btn">
                                                            <i class="fas fa-search-plus text-white"></i>
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>


                        <div class="accordion-item">
                            <h2 class="accordion-header" id="headingFour">
                                <button class="accordion-button collapsed border-0" type="button" data-bs-toggle="collapse" data-bs-target="#collapseFour" aria-expanded="false" aria-controls="collapseFour">
                                    Procesos de Selección y Colocación de Talentos
                                </button>
                            </h2>
                            <div id="collapseFour" class="accordion-collapse collapse" aria-labelledby="headingFour" data-bs-parent="#accordionExample">
                                <div class="accordion-body rounded">
                                    <span class="badge" style="background-color: #17385A">LOGRO/RESULTADO:</span>
                                    Se provee a las empresas el servicio de dotación del personal idóneo, de acuerdo con los perfiles de cargos y a las políticas de contratación establecidas, con un proceso integral que incluye la captación de hojas de vida, entrevistas por competencias, evaluaciones psicométricas, verificación de referencias y presentación de la terna para la selección final.<br><br>

                                    <span class="badge" style="background-color: #17385A">DESCRIPCIÓN DEL PROCESO:</span>
                                    <ul>
                                        <li>Reclutamiento de Candidatos</li>
                                        <li>Entrevistas por Competencias</li>
                                        <li>Aplicación de Pruebas Psicométricas</li>
                                        <li>Assessment Center (casos específicos)</li>
                                        <li>Verificación de Referencias</li>
                                    </ul>

                                    <span class="badge" style="background-color: #17385A">PRODUCTOS A ENTREGAR:</span>
                                    <ul>
                                        <li>Presentación de Terna</li>
                                    </ul>
                                </div>
                            </div>
                        </div>


                        <div class="accordion-item">
                            <h2 class="accordion-header" id="headingFive">
                                <button class="accordion-button collapsed border-0" type="button" data-bs-toggle="collapse" data-bs-target="#collapseFive" aria-expanded="false" aria-controls="collapseFive">
                                    Evaluación del desempeño 360
                                </button>
                            </h2>
                            <div id="collapseFive" class="accordion-collapse collapse" aria-labelledby="headingFive" data-bs-parent="#accordionExample">
                                <div class="accordion-body rounded">
                                    <span class="badge" style="background-color: #17385A">LOGRO/RESULTADO:</span>
                                    Se desarrolla el proceso de evaluación de desempeño, con la metodología 360º grados, que proporciona información objetiva del desempeño por competencias del personal, que contribuye a la toma de acciones pertinentes que orientan hacia la efectividad en el trabajo.<br><br>

                                    <span class="badge" style="background-color: #17385A">DESCRIPCIÓN DEL PROCESO:</span>
                                    <ul>
                                        <li>Diseño y Validación de Instrumentos de Evaluación por Competencias</li>
                                        <li>Aplicación de Instrumentos Online</li>
                                        <li>Procesamiento Automatizado de Datos</li>
                                        <li>Sensibilización al personal</li>
                                        <li>Orientación a Líderes del Proyecto</li>
                                    </ul>

                                    <span class="badge" style="background-color: #17385A">PRODUCTOS A ENTREGAR:</span>
                                    <ul>
                                        <li>Informes Individuales</li>
                                        <li>Informe Organizacional del Desempeño</li>
                                        <li>
                                            <div class="gallery-item w-100">
                                                <div class="gallery-img">
                                                    Manual&nbsp;<i class="fas fa-book"></i>
                                                    <div class="gallery-overlay">
                                                        <a href="{{ asset('img/consultorias/ayd_manual_desempeno.jpg') }}" data-lightbox="gallery" class="gallery-btn">
                                                            <i class="fas fa-search-plus text-white"></i>
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>


                    </div>
                </div>
            </div>
            <div class="col-xl-6 wow fadeInRight" data-wow-delay="0.4s">
                <img src="{{ asset('img/ayd/formacion.svg') }}" class="img-fluid w-100" alt="">
            </div>
        </div>
    </div>
</div>
<!-- FAQs End -->

<style>
/* Custom Gallery Carousel Styles */
.custom-gallery-carousel {
    width: 100%;
    max-width: 100%;
    overflow: hidden;
}

.gallery-container {
    position: relative;
    width: 100%;
}

.gallery-slides {
    position: relative;
    width: 100%;
    height: auto;
}

.gallery-slide {
    display: none;
    width: 100%;
    padding: 0 15px;
}

.gallery-slide.active {
    display: block;
}

/* Gallery Items */
.gallery-item {
    position: relative;
    overflow: hidden;
    border-radius: 10px;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    height: 100%;
    display: flex;
    flex-direction: column;
}

.gallery-item:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 35px rgba(23, 56, 90, 0.15);
}

.gallery-img {
    position: relative;
    overflow: hidden;
    border-radius: 10px;
    height: 180px;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
}

.gallery-img img {
    /* width: 10%; */
    height: auto;
    object-fit: cover;
    object-position: center;
    border-radius: 10px;
    transition: transform 0.4s ease;
}

.gallery-item:hover .gallery-img img {
    transform: scale(1.1);
}

.gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(23, 56, 90, 0.9), rgba(166, 198, 226, 0.8));
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.4s ease;
    border-radius: 10px;
}

.gallery-item:hover .gallery-overlay {
    opacity: 1;
}

.gallery-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    background: #17385A;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.gallery-btn:hover {
    background: #0BFFFF;
    transform: scale(1.2);
    box-shadow: 0 8px 25px rgba(11, 225, 255, 0.3);
}

.gallery-btn i {
    font-size: 20px;
    color: white;
}

/* Indicadores de slide */
.gallery-indicators {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 15px;
    margin-top: 40px;
    padding: 10px 0; /* Espacio adicional para el scale */
    min-height: 40px; /* Altura mínima para acomodar el scale */
}

.indicator {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: #ddd;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    flex-shrink: 0; /* Evitar que se compriman */
}

.indicator.active {
    background: #17385A;
    transform: scale(1.3);
    box-shadow: 0 4px 15px rgba(23, 56, 90, 0.3);
}

.indicator:hover {
    background: #17385A;
    transform: scale(1.1);
}

/* Lightbox Modal */
.lightbox-modal {
    display: none;
    position: fixed;
    z-index: 9999;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    animation: fadeIn 0.3s ease;
}

.lightbox-content {
    position: relative;
    margin: auto;
    padding: 20px;
    width: 90%;
    max-width: 1200px;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.lightbox-content img {
    max-width: 100%;
    max-height: 90vh;
    object-fit: contain;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

.lightbox-close {
    position: absolute;
    top: 20px;
    right: 35px;
    color: white;
    font-size: 40px;
    font-weight: bold;
    cursor: pointer;
    z-index: 10000;
}

.lightbox-close:hover {
    color: #0BFFFF;
}

/* Section styling */
.gallery {
    background: linear-gradient(135deg, #f8f9fa 0%, rgba(166, 198, 226, 0.1) 100%);
}

/* Responsive adjustments */
@media (max-width: 992px) {
    .gallery-img {
        height: 150px;
    }
}

@media (max-width: 768px) {
    .gallery-img {
        height: 130px;
    }

    .gallery-btn {
        width: 50px;
        height: 50px;
    }

    .gallery-btn i {
        font-size: 16px;
    }

    .gallery-indicators {
        margin-top: 30px;
        gap: 12px;
        padding: 8px 0;
        min-height: 35px;
    }

    .indicator {
        width: 14px;
        height: 14px;
    }
}

@media (max-width: 576px) {
    .gallery-img {
        height: 110px;
    }

    .gallery-btn {
        width: 45px;
        height: 45px;
    }

    .gallery-btn i {
        font-size: 14px;
    }

    .gallery-indicators {
        margin-top: 25px;
        gap: 10px;
        padding: 6px 0;
        min-height: 30px;
    }

    .indicator {
        width: 12px;
        height: 12px;
    }
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}
</style>

<script>
// Custom Gallery Carousel JavaScript
let currentSlide = 0;
let slides = [];
let indicators = [];
let totalSlides = 0;

// Función para inicializar elementos
function initializeElements() {
    slides = document.querySelectorAll('.gallery-slide');
    indicators = document.querySelectorAll('.indicator');
    totalSlides = slides.length;

    // Limpiar todos los estados activos
    slides.forEach(slide => slide.classList.remove('active'));
    indicators.forEach(indicator => indicator.classList.remove('active'));

    // Establecer el primer slide como activo
    currentSlide = 0;
    if (slides.length > 0 && indicators.length > 0) {
        slides[0].classList.add('active');
        indicators[0].classList.add('active');
    }
}

// Función para ir a un slide específico
function goToSlide(slideIndex) {
    // Validar índice
    if (slideIndex < 0 || slideIndex >= totalSlides) {

        return;
    }

    // Remover clase active de todos los slides e indicadores
    slides.forEach(slide => slide.classList.remove('active'));
    indicators.forEach(indicator => indicator.classList.remove('active'));

    // Actualizar índice actual
    currentSlide = slideIndex;

    // Activar el slide e indicador correspondiente
    if (slides[currentSlide]) {
        slides[currentSlide].classList.add('active');
    }
    if (indicators[currentSlide]) {
        indicators[currentSlide].classList.add('active');
    }
}
// Función para abrir lightbox
function openLightbox(imageSrc) {
    const modal = document.getElementById('lightboxModal');
    const modalImg = document.getElementById('lightboxImage');
    modal.style.display = 'block';
    modalImg.src = imageSrc;

    // Pausar autoplay cuando se abre lightbox
    clearInterval(slideInterval);
}

// Función para cerrar lightbox
function closeLightbox() {
    const modal = document.getElementById('lightboxModal');
    modal.style.display = 'none';

    // Reanudar autoplay cuando se cierra lightbox
    startAutoSlide();
}



// Inicializar cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', function() {
    // Inicializar elementos
    initializeElements();

    // Verificar que tenemos slides
    if (totalSlides === 0) {
        return;
    }

    // Cerrar lightbox con tecla Escape
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeLightbox();
        }
    });
});
</script>
<!-- Gallery End -->
<!-- Gallery End -->
