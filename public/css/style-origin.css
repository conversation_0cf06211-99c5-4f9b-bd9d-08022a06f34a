
/*** Spinner Start ***/
#spinner {
    opacity: 0;
    visibility: hidden;
    transition: opacity .5s ease-out, visibility 0s linear .5s;
    z-index: 99999;
}

#spinner.show {
    transition: opacity .5s ease-out, visibility 0s linear 0s;
    visibility: visible;
    opacity: 1;
}
/*** Spinner End ***/

.back-to-top {
    position: fixed;
    right: 30px;
    bottom: 30px;
    transition: 0.5s;
    z-index: 99;
}


/*** Button Start ***/
.btn {
    font-weight: 600;
    transition: .5s;
}

.btn-square {
    width: 32px;
    height: 32px;
}

.btn-sm-square {
    width: 34px;
    height: 34px;
}

.btn-md-square {
    width: 44px;
    height: 44px;
}

.btn-lg-square {
    width: 56px;
    height: 56px;
}

.btn-xl-square {
    width: 66px;
    height: 66px;
}

.btn-square,
.btn-sm-square,
.btn-md-square,
.btn-lg-square,
.btn-xl-square {
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: normal;
}

.btn.btn-primary {
    color: var(--bs-white);
    border: none;
}

.btn.btn-primary:hover {
    background: var(--bs-dark);
    color: var(--bs-white);
}

.btn.btn-light {
    color: var(--bs-primary);
    border: none;
}

.btn.btn-light:hover {
    color: var(--bs-white);
    background: var(--bs-dark);
}

.btn.btn-dark {
    color: var(--bs-white);
    border: none;
}

.btn.btn-dark:hover {
    color: var(--bs-primary);
    background: var(--bs-light);
}


/*** Topbar Start ***/ 
.topbar .dropdown-toggle::after {
    border: none;
    content: "\f107";
    font-family: "Font Awesome 5 Free";
    font-weight: 600;
    vertical-align: middle;
    margin-left: 8px;
    color: var(--bs-primary);
}

.topbar .dropdown .dropdown-menu a:hover {
    background: var(--bs-primary);
    color: var(--bs-white);
}

.topbar .dropdown .dropdown-menu {
    transform: rotateX(0deg);
    visibility: visible;
    background: var(--bs-light);
    padding-top: 10px;
    border: 0;
    transition: .5s;
    opacity: 1;
}
/*** Topbar End ***/


/*** Navbar Start ***/
.nav-bar {
    background: var(--bs-white);
    transition: 1s;
}

.navbar-light .navbar-brand img {
    max-height: 60px;
    transition: .5s;
}

.nav-bar .navbar-light .navbar-brand img {
    max-height: 50px;
}

.navbar .navbar-nav .nav-item .nav-link {
    padding: 0;
}

.navbar .navbar-nav .nav-item {
    display: flex;
    align-items: center;
    padding: 15px;
}

.navbar-light .navbar-nav .nav-item:hover,
.navbar-light .navbar-nav .nav-item.active,
.navbar-light .navbar-nav .nav-item:hover .nav-link,
.navbar-light .navbar-nav .nav-item.active .nav-link {
    color: var(--bs-primary);
}

@media (max-width: 991px) {
    .navbar {
        padding: 20px 0;
    }

    .navbar .navbar-nav .nav-link {
        padding: 0;
    }

    .navbar .navbar-nav .nav-item {
        display: flex;
        padding: 20px;
        flex-direction: column;
        justify-content: start;
        align-items: start;
    }

    .navbar .navbar-nav .nav-btn {
        display: flex;
        justify-content: start;
    }

    .navbar .navbar-nav {
        width: 100%;
        display: flex;
        margin-top: 20px;
        padding-bottom: 20px;
        background: var(--bs-light);
        border-radius: 10px;
    }

    .navbar.navbar-expand-lg .navbar-toggler {
        padding: 8px 15px;
        border: 1px solid var(--bs-primary);
        color: var(--bs-primary);
    }
}

.navbar .dropdown-toggle::after {
    border: none;
    content: "\f107";
    font-family: "Font Awesome 5 Free";
    font-weight: 600;
    vertical-align: middle;
    margin-left: 8px;
}

.dropdown .dropdown-menu .dropdown-item:hover {
    background: var(--bs-primary);
    color: var(--bs-white);
}

@media (min-width: 992px) {
    .navbar {
        padding: 20px 0;
    }

    .navbar .nav-btn {
        display: flex;
        align-items: center;
        justify-content: end;
    }

    .navbar .navbar-nav {
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        background: var(--bs-light);
        border-radius: 10px;
    }

    .navbar .navbar-nav .nav-btn {
        width: 100%;
        display: flex;
        margin-left: auto;
    }

    .navbar .nav-item .dropdown-menu {
        display: block;
        visibility: hidden;
        top: 100%;
        transform: rotateX(-75deg);
        transform-origin: 0% 0%;
        border: 0;
        border-radius: 10px;
        transition: .5s;
        opacity: 0;
    }

    .navbar .nav-item:hover .dropdown-menu {
        transform: rotateX(0deg);
        visibility: visible;
        margin-top: 20px;
        background: var(--bs-light);
        transition: .5s;
        opacity: 1;
    }
}
/*** Navbar End ***/


/*** Carousel Hero Header Start ***/
.header-carousel .header-carousel-item {
    height: 700px;
}

.header-carousel .owl-nav .owl-prev,
.header-carousel .owl-nav .owl-next {
    position: absolute;
    width: 60px;
    height: 60px;
    border-radius: 60px;
    background: var(--bs-light);
    color: var(--bs-primary);
    font-size: 26px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: 0.5s;
}

.header-carousel .owl-nav .owl-prev {
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    margin-left: 30px;
}
.header-carousel .owl-nav .owl-next {
    bottom: 30px;
    right: 50%;
    transform: translateX(-50%);
    margin-right: 30px;
}

.header-carousel .owl-nav .owl-prev:hover,
.header-carousel .owl-nav .owl-next:hover {
    box-shadow: inset 0 0 100px 0 var(--bs-secondary);
    color: var(--bs-white);
}

.header-carousel .header-carousel-item .carousel-caption {
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    bottom: 0;
    background: rgba(0, 0, 0, .2);
    display: flex;
    align-items: center;
}

@media (max-width: 991px) {
    .header-carousel .header-carousel-item .carousel-caption {
        padding-top: 45px;
    }

    .header-carousel .header-carousel-item {
        height: 1300px;
    }
}

@media (max-width: 767px) {
    .header-carousel .header-carousel-item {
        height: 950px;
    }

    .header-carousel .owl-nav .owl-prev {
        bottom: -30px;
        left: 50%;
        transform: translateX(-50%);
        margin-left: -45px;
    }

    .header-carousel .owl-nav .owl-next {
        bottom: -30px;
        left: 50%;
        transform: translateX(-50%);
        margin-left: 45px;
    }
}
/*** Carousel Hero Header End ***/


/*** Single Page Hero Header Start ***/
.bg-breadcrumb {
    position: relative;
    overflow: hidden;
    background: linear-gradient(rgba(1, 95, 201, 0.9), rgba(0, 0, 0, 0.2)), url(../img/bg-breadcrumb.jpg);
    background-position: center center;
    background-repeat: no-repeat;
    background-size: cover;
    padding: 60px 0 60px 0;
    transition: 0.5s;
}

.bg-breadcrumb .breadcrumb {
    position: relative;
}

.bg-breadcrumb .breadcrumb .breadcrumb-item a {
    color: var(--bs-white);
}
/*** Single Page Hero Header End ***/


/*** Feature Start ***/
.feature .feature-item {
    border-radius: 10px;
    background: var(--bs-white);
    transition: 0.5s;
}

.feature .feature-item:hover {
    background: var(--bs-primary);
}

.feature .feature-item .feature-icon {
    position: relative;
    width: 100px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
    background: var(--bs-light);
}

.feature .feature-item:hover .feature-icon i {
    z-index: 9;
}

.feature .feature-item .feature-icon::after {
    content: "";
    position: absolute;
    width: 100%;
    height: 0;
    top: 0;
    left: 0;
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
    background: var(--bs-dark);
    transition: 0.5s;
    z-index: 1;
}

.feature .feature-item:hover .feature-icon::after {
    height: 100%;
}

.feature .feature-item .feature-icon {
    color: var(--bs-primary);
}

.feature .feature-item .feature-icon,
.feature .feature-item h4,
.feature .feature-item p {
    transition: 0.5s;
}

.feature .feature-item:hover .feature-icon {
    color: var(--bs-white);
}
.feature .feature-item:hover h4,
.feature .feature-item:hover p {
    color: var(--bs-white);
}

.feature .feature-item:hover a.btn {
    background: var(--bs-white);
    color: var(--bs-primary);
}

.feature .feature-item:hover a.btn:hover {
    background: var(--bs-dark);
    color: var(--bs-white);
}
/*** Feature End ***/


/*** Service Start ***/
.service .service-item {
    border-radius: 10px;
}
.service .service-item .service-img {
    position: relative;
    overflow: hidden;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
}

.service .service-item .service-img img {
    transition: 0.5s;
}

.service .service-item:hover .service-img img {
    transform: scale(1.1);
}

.service .service-item .service-img::after {
    content: "";
    position: absolute;
    width: 100%;
    height: 0;
    top: 0;
    left: 0;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    background: rgba(1, 95, 201, .2);
    transition: 0.5s;
    z-index: 1;
}

.service .service-item:hover .service-img::after {
    height: 100%;
}

.service .service-item .service-img .service-icon {
    position: absolute;
    width: 70px;
    bottom: 0;
    right: 25px;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    background: var(--bs-light);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: 0.5s;
    z-index: 9;
}

.service .service-item .service-img .service-icon i {
    color: var(--bs-primary);
    transition: 0.5s;
}

.service .service-item:hover .service-img .service-icon i {
    transform: rotateX(360deg);
    color: var(--bs-white);
}

.service .service-item:hover .service-img .service-icon {
    bottom: 0;
    color: var(--bs-white);
    background: var(--bs-primary);
}

.service .service-content {
    position: relative;
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
    background: var(--bs-light);
}

.service .service-item .service-content .service-content-inner {
    position: relative;
    z-index: 9;
}

.service .service-item .service-content .service-content-inner .h4,
.service .service-item .service-content .service-content-inner p {
    transition: 0.5s;
}

.service .service-item:hover .service-content .service-content-inner .h4,
.service .service-item:hover .service-content .service-content-inner p {
    color: var(--bs-white);
}

.service .service-item:hover .service-content .service-content-inner .btn.btn-primary {
    color: var(--bs-primary);
    background: var(--bs-white);
}

.service .service-item:hover .service-content .service-content-inner .btn.btn-primary:hover {
    color: var(--bs-white);
    background: var(--bs-dark);
}

.service .service-item:hover .service-content .service-content-inner .h4:hover {
    color: var(--bs-dark);
}

.service .service-item .service-content::after {
    content: "";
    position: absolute;
    width: 100%;
    height: 0;
    bottom: 0;
    left: 0;
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
    background: var(--bs-primary);
    transition: 0.5s;
    z-index: 1;
}

.service .service-item:hover .service-content::after {
    height: 100%;
}
/*** Service End ***/


/*** FAQs Start ***/
.faq-section .accordion .accordion-item .accordion-header .accordion-button {
    color: var(--bs-white);
    background: rgba(1, 95, 201, .8);
    font-size: 18px;
}

.faq-section .accordion .accordion-item .accordion-header .accordion-button.collapsed {
    color: var(--bs-primary);
    background: var(--bs-light);
}
/*** FAQs End ***/


/*** Blog Start ***/
.blog .blog-item {
    border-radius: 10px;
    transition: 0.5s;
}

.blog .blog-item:hover {
    box-shadow: 0 0 45px rgba(0, 0, 0, .1);
}

.blog .blog-item:hover .blog-content {
    background: var(--bs-white);
}

.blog .blog-item .blog-img {
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    position: relative;
    overflow: hidden;
}

.blog .blog-item .blog-img img {
    transition: 0.5s;
}

.blog .blog-item:hover .blog-img img {
    transform: scale(1.2);
}

.blog .blog-item .blog-img::after {
    content: "";
    position: absolute;
    width: 100%;
    height: 0;
    top: 0;
    left: 0;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    background: rgba(1, 95, 201, .2);
    transition: 0.5s;
    z-index: 1;
}

.blog .blog-item:hover .blog-img::after {
    height: 100%;
}

.blog .blog-item .blog-img .blog-categiry {
    position: absolute;
    bottom: 0;
    right: 0;
    border-top-left-radius: 10px;
    display: inline-flex;
    color: var(--bs-white);
    background: var(--bs-primary);
    z-index: 9;
}

.blog .blog-item .blog-content {
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
    background: var(--bs-light);
}

.blog .blog-item .blog-content a.btn {
    color: var(--bs-dark);
}

.blog .blog-item:hover .blog-content a.btn:hover {
    color: var(--bs-primary);
}
/*** Blog End ***/


/*** Team Start ***/
.team .team-item .team-img {
    position: relative;
    overflow: hidden;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
} 

.team .team-item .team-img img {
    transition: 0.5s;
}

.team .team-item:hover .team-img img {
    transform: scale(1.1);
}

.team .team-item .team-img::after {
    content: "";
    position: absolute;
    width: 100%;
    height: 0;
    top: 0;
    left: 0;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    transition: 0.5s;
}

.team .team-item:hover .team-img::after {
    height: 100%;
    background: rgba(255, 255, 255, 0.3);
}

.team .team-item .team-img .team-icon {
    position: absolute;
    bottom: 20px;
    right: 20px;
    margin-bottom: -100%;
    transition: 0.5s;
    z-index: 9;
}

.team .team-item:hover .team-img .team-icon {
    margin-bottom: 0;
}

.team .team-item .team-title {
    color: var(--bs-white);
    background: var(--bs-primary);
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
    transition: 0.5s;
}

.team .team-item .team-title h4 {
    color: var(--bs-white);
}

.team .team-item:hover .team-title {
    background: var(--bs-dark);
}
/*** Team End ***/


/*** Testimonial Start ***/
.testimonial-carousel .owl-stage-outer {
    margin-top: 58px;
    margin-right: -1px;
}

.testimonial .owl-nav .owl-prev {
    position: absolute;
    top: -58px;
    left: 0;
    background: var(--bs-primary);
    color: var(--bs-white);
    padding: 5px 30px;
    border-radius: 30px;
    transition: 0.5s;
}

.testimonial .owl-nav .owl-prev:hover {
    background: var(--bs-dark);
    color: var(--bs-white);
}

.testimonial .owl-nav .owl-next {
    position: absolute;
    top: -58px;
    right: 0;
    background: var(--bs-primary);
    color: var(--bs-white);
    padding: 5px 30px;
    border-radius: 30px;
    transition: 0.5s;
}

.testimonial .owl-nav .owl-next:hover {
    background: var(--bs-dark);
    color: var(--bs-white);
}
/*** Testimonial End ***/


/*** Contact Start ***/
.contact .contact-img {
    position: relative;
}

.contact .contact-img::after {
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
    background: rgba(255, 255, 255, 0.9);
    z-index: 1;
    animation-name: image-efects;
    animation-duration: 10s;
    animation-delay: 1s;
    animation-iteration-count: infinite;
    animation-direction: alternate;
    transition: 0.5s
}

@keyframes image-efects {
    0%  {border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;}

    25% {border-radius: 35% 65% 27% 73% / 71% 25% 75% 29%;}

    50% {border-radius: 41% 59% 45% 55% / 32% 21% 79% 68%;}

    75% {border-radius: 69% 31% 39% 61% / 70% 83% 17% 30%;}

    100% {border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;}
}

.contact .contact-img .contact-img-inner {
    position: relative;
    z-index: 9;
}
/*** Contact End ***/


/*** Footer Start ***/
.footer {
    background: var(--bs-dark);
}

.footer .footer-item {
    display: flex;
    flex-direction: column;
}

.footer .footer-item a {
    line-height: 35px;
    color: var(--bs-body);
    transition: 0.5s;
}

.footer .footer-item p {
    line-height: 35px;
}

.footer .footer-item a:hover {
    color: var(--bs-primary);
}

.footer .footer-item .footer-btn a,
.footer .footer-item .footer-btn a i {
    transition: 0.5s;
}

.footer .footer-item .footer-btn a:hover {
    background: var(--bs-white);
}

.footer .footer-item .footer-btn a:hover i {
    color: var(--bs-primary);
}


.footer .footer-item .footer-instagram {
    position: relative;
    overflow: hidden;
}

.footer .footer-item .footer-instagram img {
    transition: 0.5s;
}

.footer .footer-item .footer-instagram:hover img {
    transform: scale(1.2);
}

.footer .footer-item .footer-instagram .footer-search-icon {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: 0.5s;
    opacity: 0;
}

.footer .footer-item .footer-instagram:hover .footer-search-icon {
    opacity: 1;
    background: rgba(0, 0, 0, .6);
}

.footer .footer-item .footer-btn a {
    background: var(--bs-light);
    color: var(--bs-primary);
}

.footer .footer-item .footer-btn a:hover {
    background: var(--bs-primary);
}

.footer .footer-item .footer-btn a:hover i {
    color: var(--bs-white);
}
/*** Footer End ***/


/*** copyright Start ***/
.copyright {
    background: var(--bs-dark);
    border-top: 1px solid rgba(255, 255, 255, .08);
}
/*** copyright end ***/


